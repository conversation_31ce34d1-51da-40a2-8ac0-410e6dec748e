package rest

import (
	"net/http"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/workarea/model"
	"github.com/go-playground/validator/v10"
	"github.com/sirupsen/logrus"
)

type WorkAreaHandler interface {
	Create(w http.ResponseWriter, r *http.Request)
	Update(w http.ResponseWriter, r *http.Request)
	GetById(w http.ResponseWriter, r *http.Request)
	GetAll(w http.ResponseWriter, r *http.Request)
	Delete(w http.ResponseWriter, r *http.Request)
}

type workAreaHandler struct {
	log       *logrus.Logger
	validator *validator.Validate
	useCase   model.WorkAreaUsecase
}

func NewWorkAreaHandler(
	log *logrus.Logger,
	validator *validator.Validate,
	useCase model.WorkAreaUsecase,
) WorkAreaHandler {
	return &workAreaHandler{
		log:       log,
		validator: validator,
		useCase:   useCase,
	}
}
