package app

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/workarea/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
)

type workAreaUsecase struct {
	repo model.WorkAreaRepository
}

func (w *workAreaUsecase) Create(ctx context.Context, workArea model.WorkAreaCreate) (string, error) {
	// Check if code already exists
	codeExists, err := w.repo.CountByProp(ctx, "code", workArea.Code)
	if err != nil {
		return "", utils.InternalErrorf("Failed to check if work area code exists", err, nil)
	}

	if codeExists > 0 {
		return "", model.WorkAreaConflictCodef("Work area code already exists", nil, nil)
	}

	// Check if name already exists
	nameExists, err := w.repo.CountByProp(ctx, "name", workArea.Name)
	if err != nil {
		return "", utils.InternalErrorf("Failed to check if work area name exists", err, nil)
	}

	if nameExists > 0 {
		return "", model.WorkAreaConflictNamef("Work area name already exists", nil, nil)
	}

	newWorkArea := model.WorkArea{
		ID:   utils.UniqueId(),
		Code: workArea.Code,
		Name: workArea.Name,
	}

	err = w.repo.Create(ctx, newWorkArea)
	if err != nil {
		return "", err
	}

	return newWorkArea.ID, nil
}

func (w *workAreaUsecase) Update(ctx context.Context, workArea model.WorkAreaUpdate) error {
	// Get the current work area to check if code/name has changed
	currentWorkArea, err := w.repo.GetByProp(ctx, "id", workArea.ID)
	if err != nil {
		return err
	}

	// Check if code has changed and if it's already in use
	if currentWorkArea.Code != workArea.Code {
		codeExists, err := w.repo.CountByProp(ctx, "code", workArea.Code)
		if err != nil {
			return utils.InternalErrorf("Failed to check if work area code exists", err, nil)
		}

		if codeExists > 0 {
			return model.WorkAreaConflictCodef("Work area code already exists", nil, nil)
		}
	}

	// Check if name has changed and if it's already in use
	if currentWorkArea.Name != workArea.Name {
		nameExists, err := w.repo.CountByProp(ctx, "name", workArea.Name)
		if err != nil {
			return utils.InternalErrorf("Failed to check if work area name exists", err, nil)
		}

		if nameExists > 0 {
			return model.WorkAreaConflictNamef("Work area name already exists", nil, nil)
		}
	}

	updatedWorkArea := model.WorkArea{
		ID:   workArea.ID,
		Code: workArea.Code,
		Name: workArea.Name,
	}

	err = w.repo.Update(ctx, updatedWorkArea)
	if err != nil {
		return err
	}

	return nil
}

func (w *workAreaUsecase) GetByProp(ctx context.Context, prop string, value string) (*model.WorkArea, error) {
	return w.repo.GetByProp(ctx, prop, value)
}

func (w *workAreaUsecase) GetAll(ctx context.Context) ([]model.WorkArea, error) {
	return w.repo.GetAll(ctx)
}

func (w *workAreaUsecase) Delete(ctx context.Context, id string) error {
	return w.repo.Delete(ctx, id)
}

func NewWorkAreaUsecase(repo model.WorkAreaRepository) model.WorkAreaUsecase {
	return &workAreaUsecase{
		repo: repo,
	}
}
