package repo

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/category/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/category/repo/pg"
)

type categoryRepository struct {
	pgRepo pg.CategoryPostgreRepo
}

// CountByProp implements model.CategoryRepository.
func (c *categoryRepository) CountByProp(ctx context.Context, prop string, value string) (int, error) {
	return c.pgRepo.CountByProp(ctx, prop, value)
}

// Create implements model.CategoryRepository.
func (c *categoryRepository) Create(ctx context.Context, category model.Category) error {
	return c.pgRepo.Create(ctx, category)
}

// Delete implements model.CategoryRepository.
func (c *categoryRepository) Delete(ctx context.Context, id string) error {
	return c.pgRepo.Delete(ctx, id)
}

// GetAll implements model.CategoryRepository.
func (c *categoryRepository) GetAll(ctx context.Context) ([]model.Category, error) {
	return c.pgRepo.GetAll(ctx)
}

// GetByProp implements model.CategoryRepository.
func (c *categoryRepository) GetByProp(ctx context.Context, prop string, value string) (*model.Category, error) {
	return c.pgRepo.GetByProp(ctx, prop, value)
}

// Update implements model.CategoryRepository.
func (c *categoryRepository) Update(ctx context.Context, category model.Category) error {
	return c.pgRepo.Update(ctx, category)
}

// GetSubcategories implements model.CategoryRepository.
func (c *categoryRepository) GetSubcategories(ctx context.Context, categoryID string) ([]model.Category, error) {
	return c.pgRepo.GetSubcategories(ctx, categoryID)
}

// GetParentCategory implements model.CategoryRepository.
func (c *categoryRepository) GetParentCategory(ctx context.Context, categoryID string) (*model.Category, error) {
	return c.pgRepo.GetParentCategory(ctx, categoryID)
}

func NewCategoryRepository(pgRepo pg.CategoryPostgreRepo) model.CategoryRepository {
	return &categoryRepository{
		pgRepo: pgRepo,
	}
}
