package app

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/brand/model"
)

type brandUsecase struct {
	repo model.BrandRepository
}

// Delete implements model.BrandUsecase.
func (b *brandUsecase) Delete(ctx context.Context, id string) error {
	return b.repo.Delete(ctx, id)
}

// GetAll implements model.BrandUsecase.
func (b *brandUsecase) GetAll(ctx context.Context) ([]model.Brand, error) {
	return b.repo.GetAll(ctx)
}

// GetByProp implements model.BrandUsecase.
func (b *brandUsecase) GetByProp(ctx context.Context, prop string, value string) (*model.Brand, error) {
	return b.repo.GetByProp(ctx, prop, value)
}

func NewBrandUsecase(repo model.BrandRepository) model.BrandUsecase {
	return &brandUsecase{
		repo: repo,
	}
}
